#!/usr/bin/env python3
"""
Debug script to test LLM service and identify the JSON parsing issue.
"""

import sys
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import settings
from core.llm_service import llm_service
from core.models import LLMMessage

# Configure logging to see debug messages
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_basic_completion():
    """Test basic completion functionality."""
    print("Testing basic completion...")
    try:
        response = llm_service.generate_completion(
            system_prompt="You are a helpful assistant.",
            user_prompt="Say hello in a friendly way."
        )
        print(f"✓ Basic completion successful: {response[:100]}...")
        return True
    except Exception as e:
        print(f"✗ Basic completion failed: {e}")
        return False

def test_structured_response():
    """Test structured response functionality."""
    print("\nTesting structured response...")
    try:
        schema = {
            "type": "object",
            "properties": {
                "response": {
                    "type": "string",
                    "description": "A friendly greeting"
                },
                "follow_up_questions": {
                    "type": "array",
                    "description": "List of follow-up questions",
                    "items": {"type": "string"}
                }
            },
            "required": ["response", "follow_up_questions"],
            "additionalProperties": False
        }
        
        response = llm_service.generate_structured_response(
            system_prompt="You are a helpful assistant.",
            user_prompt="Say hello and suggest 2 follow-up questions.",
            schema=schema
        )
        print(f"✓ Structured response successful: {response}")
        return True
    except Exception as e:
        print(f"✗ Structured response failed: {e}")
        return False

def test_llm_messages_structured():
    """Test structured response with LLM messages."""
    print("\nTesting structured response with LLM messages...")
    try:
        messages = [
            LLMMessage("system", "You are a helpful assistant."),
            LLMMessage("user", "Say hello and suggest 2 follow-up questions.")
        ]
        
        schema = {
            "type": "object",
            "properties": {
                "response": {
                    "type": "string",
                    "description": "A friendly greeting"
                },
                "follow_up_questions": {
                    "type": "array",
                    "description": "List of follow-up questions",
                    "items": {"type": "string"}
                }
            },
            "required": ["response", "follow_up_questions"],
            "additionalProperties": False
        }
        
        response = llm_service.generate_structured_response_from_llm_messages(
            messages=messages,
            schema=schema
        )
        print(f"✓ LLM messages structured response successful: {response}")
        return True
    except Exception as e:
        print(f"✗ LLM messages structured response failed: {e}")
        return False

def test_conversation_scenario():
    """Test a conversation scenario similar to what happens in Telegram."""
    print("\nTesting conversation scenario...")
    try:
        messages = [
            LLMMessage("system", "You are a helpful digital twin assistant. Respond naturally and provide follow-up questions."),
            LLMMessage("user", "Hello, how are you today?")
        ]
        
        schema = {
            "type": "object",
            "properties": {
                "response": {
                    "type": "string",
                    "description": "Natural response to the user's message"
                },
                "follow_up_questions": {
                    "type": "array",
                    "description": "3 recommended questions for users to ask. Each question should only be up to 7 words long.",
                    "items": {"type": "string"}
                }
            },
            "required": ["response", "follow_up_questions"],
            "additionalProperties": False
        }
        
        response = llm_service.generate_structured_response_from_llm_messages(
            messages=messages,
            schema=schema
        )
        print(f"✓ Conversation scenario successful: {response}")
        return True
    except Exception as e:
        print(f"✗ Conversation scenario failed: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 50)
    print("LLM SERVICE DEBUG TESTS")
    print("=" * 50)
    
    # Validate settings first
    try:
        settings.validate()
        print("✓ Settings validation passed")
    except Exception as e:
        print(f"✗ Settings validation failed: {e}")
        return False
    
    print(f"Using model: {settings.OPENAI_MODEL}")
    print(f"Max tokens: {settings.MAX_TOKENS}")
    print(f"Temperature: {settings.TEMPERATURE}")
    
    # Run tests
    tests = [
        test_basic_completion,
        test_structured_response,
        test_llm_messages_structured,
        test_conversation_scenario
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("SUMMARY")
    print("=" * 50)
    passed = sum(results)
    total = len(results)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All tests passed! The LLM service is working correctly.")
    else:
        print("✗ Some tests failed. Check the error messages above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
